import polars as pl
import numpy as np
import os
import pandas as pd

date_list = pd.read_csv("./date_list.csv", header=None)[0].to_list()
date_list = [str(i) for i in date_list]

df = pl.read_parquet("/home/<USER>/AAA/diskBig/forBt.parquet")

model_name = "GRU"
checkpoint_path = f"/home/<USER>/tslib/projs/stock1m/checkpoints/daysection0810202155"

test_pred = []
for fold in sorted(os.listdir(checkpoint_path)):
    test_pred.append(np.load(os.path.join(checkpoint_path, fold, "test_preds.npy")))
test_pred = np.concatenate(test_pred, axis=0)  



valid_dn = np.load('/disk4/shared/intern/laiyc/forModel/valid_dn.npy')
valid_dn = valid_dn[720: -100, :]
valid_dn_expanded = valid_dn[..., np.newaxis, np.newaxis]  # 变成 [1680, 6000, 1, 1]
valid_dn_expanded = np.tile(valid_dn_expanded, (1, 1, 241, 1))


valid_pred = test_pred * valid_dn_expanded
valid_pred_flatten = valid_pred.reshape(-1)
valid_dn_expanded_flatten = valid_dn_expanded.reshape(-1)

valid_pred_flatten = valid_pred_flatten[valid_dn_expanded_flatten]


pred_df = pl.DataFrame({f"{model_name}": valid_pred_flatten})

pred_df.write_parquet(f"./model_factor/{model_name}.parquet")






factor_cols = ['GRU_demean', 'GRU_SAttn_demean', 'GRU', 'lgb', 'SAttn_GRU_demean']

lfs = [
    pl.scan_parquet(f"./model_factor/{factor_col}.parquet") for factor_col in factor_cols
]

factor_df = pl.concat(lfs, how="horizontal").collect()

df = pl.concat([df, factor_df], how="horizontal")
df

# group_by datetime, calculate corr between factor and lgb  (spearman)
df = df.filter(pl.col("hhmm").is_in(["0959", "1029", "1059", "1129", "1329", "1359", "1429"]))


df = df.fill_nan(0.0)



# ---------- 0. 基础设置 ----------
dt_col     = ["datetime"]                                    # 截面时间列
n_factors = len(factor_cols)


from itertools import combinations

# ---------- 1. 构造 Spearman 相关表达式 ----------
# 相关系数对数 = n*(n-1)/2，提前生成表达式列表以走全向量化流水线
corr_exprs = [
    pl.corr(a, b, method="spearman").alias(f"{a}__{b}")
    for a, b in combinations(factor_cols, 2)
]

# ---------- 2. 在每个截面内计算相关向量 ----------
corr_by_date_lazy = (
    df.lazy()
      .group_by(dt_col)
      .agg(corr_exprs)          # 每行是一条日期记录，上三角向量展平成列
      .fill_nan(0.0)
).collect()

# ---------- 3. 沿时间轴求平均相关 ----------
avg_corr_vec = (
    corr_by_date_lazy
      .select(pl.exclude(dt_col).mean().fill_nan(0.0))  # 对每个列求平均
      .to_dicts()[0]                      # -> {"f1__f2": 0.23, ...}
)

# ---------- 4. 重构为对称相关矩阵 ----------
mat = np.eye(n_factors, dtype=np.float32)
for i in range(n_factors):
    for j in range(i + 1, n_factors):
        v = avg_corr_vec[f"{factor_cols[i]}__{factor_cols[j]}"]
        mat[i, j] = mat[j, i] = v



import numpy as np
mat = np.eye(n_factors, dtype=np.float32)
for i in range(n_factors):
    for j in range(i + 1, n_factors):
        v = avg_corr_vec[f"{factor_cols[i]}__{factor_cols[j]}"]
        mat[i, j] = mat[j, i] = v

avg_corr_df = (
    pl.DataFrame(mat, schema=factor_cols)
      .with_columns(pl.Series("factor", factor_cols))
      .select(["factor", *factor_cols])
)

avg_corr_df

avg_corr_df.write_parquet("corr_df.parquet")

"""
分钟频因子回测类
用于评估分钟级Alpha因子的表现
"""

import polars as pl
import numpy as np
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
import warnings
warnings.filterwarnings('ignore')

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns
# 使用更简洁的样式，不预设网格线
sns.set_style("white")
# 设置字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

import gc

class MinuteFactorBacktest:
    """分钟频因子回测类"""
    
    def __init__(self, data_dir: str = "diskBig", save_dir: str = "/disk4/shared/intern/laiyc/alpha", mode: str = "expr"):
        """
        初始化回测类
        
        参数:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.save_dir = Path(save_dir)
        self.mode = mode
        self.specific_times = ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]
        
        if mode == "expr":
            # 加载数据
            print("正在加载数据...")
            self.df_ohlc = pl.read_parquet(self.data_dir / "OHLCVA_Vwap.parquet")
            self.df_y = pl.read_parquet(self.data_dir / "y1.parquet")
            
            # 拼接数据
            self.df = self.df_ohlc.hstack(self.df_y)
            print(f"数据加载完成，总行数: {self.df.shape[0]:,}")

        
    
    def backtest_factor(self, factor_expr_or_df: pl.Expr | List[pl.Expr] | pl.DataFrame, factor_name: str='factor') -> Dict[str, Any]:
        """
        回测单个因子

        参数:
            factor_expr_or_df: 因子表达式、表达式列表或预计算的因子DataFrame
            factor_name: 因子名称

        返回:
            回测结果字典
        """
        print(f"\n开始回测因子: {factor_name}")
        start_time = time.time()

        # 1. 根据输入类型处理因子数据
        factor_start = time.time()

        if isinstance(factor_expr_or_df, pl.DataFrame):
            print("直接回测已有因子DataFrame...")
            # df_with_factor = self.df.hstack(factor_expr_or_df)
            df_with_factor = factor_expr_or_df
            required_cols = ["date", "hhmm", "datetime", "symbol", "y1", "factor"]
            if not all(col in df_with_factor.columns for col in required_cols):
                raise ValueError(f"数据中缺少必要列: {required_cols}")
        else:
            # 如果传入的是表达式，计算因子值
            print("正在计算因子值...")
            df_with_factor = self.df
            if isinstance(factor_expr_or_df, list):
                for expr in factor_expr_or_df:
                    df_with_factor = df_with_factor.with_columns(expr)
                df_with_factor = df_with_factor.select([
                    pl.col("date"),
                    pl.col("symbol"),
                    pl.col("hhmm"),
                    pl.col("datetime"),
                    pl.col("y1"),
                    pl.col(factor_expr_or_df[-1].meta.output_name()).alias("factor")
                ])
            else:
                df_with_factor = self.df.with_columns(factor_expr_or_df.alias("factor"))

        factor_time = time.time() - factor_start
        
        # 2. 计算因子基本信息
        factor_stats = self._calculate_factor_stats(df_with_factor)
        
        # 3. 计算IC相关指标
        print("正在计算IC指标...")
        ic_results = self._calculate_ic_metrics(df_with_factor)

        # 5. 生成因子分布数据
        distribution_data = self._get_distribution_data(df_with_factor)

        # 6. 判断因子方向并调整因子值用于十档分析
        if "error" not in ic_results and "global_ic" in ic_results:
            mean_ic = ic_results["global_ic"]["mean_ic"]
            if mean_ic < 0:
                # 反向因子，将因子值取反进行十档分析
                df_with_factor = df_with_factor.with_columns([
                    (-pl.col("factor")).alias("factor")
                ])

        # 6. 计算十档分组分析
        print("正在计算十档分组分析...")
        decile_analysis = self._calculate_decile_analysis(df_with_factor)

        # 6. 整合结果
        results = {
            "factor_name": factor_name,
            "test_start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "factor_computation_time": factor_time,
            "total_backtest_time": time.time() - start_time,
            "factor_stats": factor_stats,
            "ic_results": ic_results,
            "distribution_data": distribution_data,
            "decile_analysis": decile_analysis,
        }

        # 7. save parquet
        if self.mode == "expr":
            df_with_factor.select(pl.col("factor").alias(factor_name)).write_parquet(self.save_dir / f"{factor_name}.parquet")
        
        print(f"因子 {factor_name} 回测完成，耗时: {results['total_backtest_time']:.2f}秒")
        return results
    
    def _calculate_factor_stats(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:
        """计算因子基本统计信息"""
        stats = df_with_factor.select([
            pl.col("factor").len().alias("total_rows"),
            pl.col("factor").is_nan().sum().alias("nan_rows"),
            pl.col("factor").is_null().sum().alias("null_rows"),
            pl.col("factor").filter(~pl.col("factor").is_nan() & ~pl.col("factor").is_null()).mean().alias("mean"),
            pl.col("factor").filter(~pl.col("factor").is_nan() & ~pl.col("factor").is_null()).std().alias("std"),
            pl.col("factor").min().alias("min"),
            pl.col("factor").max().alias("max"),
            pl.col("factor").median().alias("median"),
            pl.col("factor").quantile(0.25).alias("q25"),
            pl.col("factor").quantile(0.75).alias("q75")
        ]).to_dicts()[0]
        
        # 计算有效值比例
        valid_rows = stats["total_rows"] - stats["nan_rows"] - stats["null_rows"]
        stats["valid_rows"] = valid_rows
        stats["valid_ratio"] = valid_rows / stats["total_rows"] if stats["total_rows"] > 0 else 0
        
        return stats
    
    def _calculate_ic_metrics(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:
        """计算IC相关指标"""
        # 筛选特定时间点的数据
        df_ic = df_with_factor.filter(pl.col("hhmm").is_in(self.specific_times))

        # 计算每个时间点的IC
        ic_df = df_ic.drop_nulls(["factor", "y1"]).group_by(["datetime"]).agg([
            pl.col("factor").len().alias("sample_size"),
            pl.corr("factor", "y1", method="spearman").alias("ic"),
            pl.col("hhmm").first()
        ]).filter(
            (pl.col("ic").is_not_nan()) &
            (pl.col("ic").is_not_null())
        )

        if ic_df.height == 0:
            return {"error": "无有效IC数据"}

        # 全局IC统计 
        global_ic_stats = ic_df.drop_nulls(["ic"]).select([
            pl.col("ic").mean().alias("mean_ic"),
            pl.col("ic").std().alias("ic_std"),
            (pl.col("ic") > 0).mean().alias("ic_positive_ratio"),
            pl.col("ic").len().alias("ic_count"), 
            pl.col("sample_size").mean().alias("xs_size_mean")
        ]).to_dicts()[0]

        # 计算ICIR
        icir = global_ic_stats["mean_ic"] / global_ic_stats["ic_std"] * np.sqrt(244) if global_ic_stats["ic_std"] > 0 else 0
        global_ic_stats["icir"] = icir

        # 计算月度IC - 生成热力图矩阵格式
        monthly_ic_data = None
        try:
            # 添加年月和日期列
            ic_with_date = ic_df.with_columns([
                pl.col("datetime").dt.strftime("%Y-%m").alias("year_month"),
            ])

            # 按年月分组计算IC均值
            monthly_grouped = (
                ic_with_date
                .group_by(["year_month"])
                .agg(pl.mean("ic").alias("mean_ic"))
            )

            # 检查是否有足够的数据进行透视
            if monthly_grouped.height > 0:
                # 转换为pandas进行透视操作（Polars的pivot功能有限）
                monthly_pd = monthly_grouped.to_pandas()
                monthly_ic_data = monthly_pd.set_index("year_month")
            else:
                print("没有足够的数据生成月度IC热力图")

        except Exception as e:
            print(f"计算月度IC数据时出错: {e}")
            monthly_ic_data = None

        # 按时间点分组的IC统计 - 直接在polars中计算
        time_ic_stats = {}
        for time_point in self.specific_times:
            time_ic_df = ic_df.filter(pl.col("hhmm") == time_point)

            if time_ic_df.height > 0:
                time_stats = time_ic_df.select([
                    pl.col("ic").mean().alias("mean_ic"),
                    pl.col("ic").std().alias("ic_std"),
                    (pl.col("ic") > 0).mean().alias("ic_positive_ratio"),
                    pl.col("ic").len().alias("ic_count")
                ]).to_dicts()[0]

                # 计算ICIR
                time_stats["icir"] = time_stats["mean_ic"] / time_stats["ic_std"] * np.sqrt(244) if time_stats["ic_std"] > 0 else 0
                time_ic_stats[time_point] = time_stats
            else:
                time_ic_stats[time_point] = {
                    "mean_ic": 0, "ic_std": 0, "icir": 0,
                    "ic_positive_ratio": 0, "ic_count": 0
                }

        return {
            "global_ic": global_ic_stats,
            "time_ic_stats": time_ic_stats,
            "monthly_ic_data": monthly_ic_data
        }
    
    def _get_distribution_data(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:
        """获取因子分布数据"""
        # 采样数据以减少计算量
        sample_size = min(100000, df_with_factor.height)
        df_sample = df_with_factor.sample(sample_size, seed=42)

        factor_values = df_sample.drop_nulls(["factor"])["factor"].to_numpy()

        if len(factor_values) == 0:
            return {"error": "无有效因子值"}

        return {
            "values": factor_values.tolist(),
            "sample_size": len(factor_values)
        }

    def _calculate_decile_analysis(self, df_with_factor: pl.DataFrame) -> Dict[str, Any]:
        """计算十档分组分析"""
        # 筛选特定时间点的数据
        df_decile = df_with_factor.filter(pl.col("hhmm").is_in(self.specific_times))

        # 去除空值
        df_clean = df_decile.drop_nulls(["factor", "y1"]).drop_nans(["factor", "y1"])

        if df_clean.height == 0:
            return {"error": "无有效数据进行十档分析"}

        # 计算每个datetime的平均y1（用于计算超额收益）
        df_with_avg_y1 = df_clean.with_columns([
            pl.col("y1").mean().over(["datetime"]).alias("avg_y1_by_datetime")
        ])

        # 按时间点分组，计算因子十分位数
        df_with_decile = df_with_avg_y1.with_columns([
            pl.col("factor").rank(method='random').qcut(10, labels=[f"{i+1}" for i in range(10)], allow_duplicates=True).over(["datetime"]).alias("decile")
        ])

        # 计算每个十分位的平均超额收益率（减去datetime平均值）
        decile_returns = df_with_decile.group_by(["datetime", "decile"]).agg([
            (pl.col("y1") - pl.col("avg_y1_by_datetime")).mean().alias("mean_ex_return"),
            pl.col("y1").count().alias("count")
        ]).sort(["datetime", "decile"])

        # 计算累积PnL
        decile_pnl = decile_returns.with_columns([
            (pl.col("mean_ex_return")).cum_sum().over("decile").alias("cumulative_pnl")
        ])

        # 计算每个十分位的总体统计
        decile_stats = decile_returns.group_by("decile").agg([
            pl.col("mean_ex_return").mean().alias("avg_return"),
            pl.col("mean_ex_return").std().alias("return_std"),
            pl.col("count").sum().alias("total_count")
        ]).sort("decile")

        return {
            "decile_returns": decile_returns.to_pandas(),
            "decile_pnl": decile_pnl.to_pandas(),
            "decile_stats": decile_stats.to_pandas()
        }
    
    def generate_report(self, results: Dict[str, Any], output_dir: str = "icBacktestLog") -> None:
        """生成回测报告"""
        output_path = Path(output_dir)
        json_dir = output_path / "json"
        img_dir = output_path / "img"
        json_dir.mkdir(parents=True, exist_ok=True)
        img_dir.mkdir(parents=True, exist_ok=True)

        factor_name = results["factor_name"]

        # 生成JSON报告
        json_path = json_dir / f"{factor_name}_report.json"
        self._generate_json_report(results, json_path)

        # 生成PDF报告
        pdf_path = img_dir / f"{factor_name}_report.pdf"
        self._generate_pdf_report(results, pdf_path)

        print(f"报告已生成:")
        print(f"  JSON: {json_path}")
        print(f"  PDF: {pdf_path}")
    
    def _generate_pdf_report(self, results: Dict[str, Any], pdf_path: Optional[Path] = None, only_pos=False) -> None:
        """生成PDF报告"""
        fig = None
        gs = None
        
        try:
            # 使用A4比例 (8.27 x 11.69 inches)
            fig = plt.figure(figsize=(8.27, 11.69))

            # 设置整体布局 - 5行2列
            gs = fig.add_gridspec(5, 2, height_ratios=[0.04, 0.12, 0.6, 0.6, 0.6], width_ratios=[1, 1],
                                hspace=0.6, wspace=0.4)

            # 标题
            fig.suptitle(f'Factor Backtest Report: {results["factor_name"]}', 
                        fontsize=16, fontweight='bold', y=0.92)

            # 时间信息
            ax_time = fig.add_subplot(gs[0, :])
            self._plot_time_info(ax_time, results)

            # 基本统计信息
            ax_stats = fig.add_subplot(gs[1, 0])
            self._plot_basic_stats(ax_stats, results)

            # 全局IC指标
            ax_global_ic = fig.add_subplot(gs[1, 1])
            self._plot_global_ic(ax_global_ic, results)

            # 因子分布图
            ax_dist = fig.add_subplot(gs[2, 0])
            self._plot_distribution(ax_dist, results)

            # 分时间点IC趋势图
            ax_trend = fig.add_subplot(gs[2, 1])
            self._plot_ic_trend(ax_trend, results)

            # 十档收益率分组图
            ax_decile_returns = fig.add_subplot(gs[3, 0])
            self._plot_decile_returns(ax_decile_returns, results)

            # 月度IC热力图（第五行右侧）
            ax_heatmap = fig.add_subplot(gs[3, 1])
            self._plot_monthly_ic_heatmap(ax_heatmap, results)

            # 十档PnL曲线
            ax_decile_pnl = fig.add_subplot(gs[4, :])
            self._plot_decile_pnl(ax_decile_pnl, results, only_pos)

            plt.tight_layout()

            if pdf_path:
                with PdfPages(pdf_path) as pdf:
                    pdf.savefig(fig, bbox_inches='tight')
                # 保存PDF后立即清理
                plt.close(fig)
            else:
                plt.show()
                # 在Jupyter中显示后不立即清理，让图形正常显示

        except Exception as e:
            print(f"PDF生成失败: {e}")
            # 出错时才强制清理
            if fig is not None:
                plt.close(fig)
        finally:
            # 只在非显示模式下进行全局清理
            if pdf_path is not None:
                plt.close('all')
                gc.collect()

    def _plot_decile_returns(self, ax, results):
        """绘制十档收益率分组图"""
        if "decile_analysis" not in results or "error" in results["decile_analysis"]:
            ax.text(0.5, 0.5, "Decile analysis data unavailable", ha='center', va='center', transform=ax.transAxes)
            ax.set_title("Factor Decile Returns", fontsize=8, fontweight='bold')
            return

        decile_stats = results["decile_analysis"]["decile_stats"]

        # 绘制柱状图
        bars = ax.bar(range(1, 11), decile_stats["avg_return"],
                     color=['red' if i in [0, 9] else 'lightblue' for i in range(10)],
                     alpha=0.7, edgecolor='black', linewidth=0.5)

        # 设置标签和标题
        ax.set_ylabel("Average Excess Return", fontsize=8)
        ax.set_title("Factor Decile Excess Returns", fontsize=8, fontweight='bold')
        ax.set_xticks(range(1, 11))
        ax.set_xticklabels([f"D{i}" for i in range(1, 11)], fontsize=8, rotation=30)

        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)

    def _plot_decile_pnl(self, ax, results, only_pos: bool = False):
        """
        绘制十档PnL曲线
        
        参数:
            only_pos: 是否只显示第10组的PnL曲线
        """
        if "decile_analysis" not in results or "error" in results["decile_analysis"]:
            ax.text(0.5, 0.5, "Decile analysis data unavailable", ha='center', va='center', transform=ax.transAxes)
            ax.set_title("Decile PnL Curves", fontsize=8, fontweight='bold')
            return

        decile_pnl = results["decile_analysis"]["decile_pnl"]
        decile_returns = results["decile_analysis"]["decile_returns"]

        import pandas as pd
        import matplotlib.dates as mdates

        # 计算第10组的夏普比率
        decile_10_returns = decile_returns[decile_returns["decile"] == "10"]["mean_ex_return"]
        if len(decile_10_returns) > 0:
            sharpe_ratio = decile_10_returns.mean() / decile_10_returns.std() if decile_10_returns.std() > 0 else 0
            annualized_sharpe = sharpe_ratio * np.sqrt(244)
        else:
            annualized_sharpe = 0

        if only_pos:
            # 只绘制第10组的PnL曲线
            decile_data = decile_pnl[decile_pnl["decile"] == "10"]
            if len(decile_data) > 0:
                x_values = pd.to_datetime(decile_data["datetime"]) if "datetime" in decile_data.columns else range(len(decile_data))
                ax.plot(x_values, decile_data["cumulative_pnl"], 
                       label="D10", linewidth=2.5, alpha=0.9, color='red')
        else:
            # 绘制所有组的PnL曲线
            for i, decile in enumerate([f"{j+1}" for j in range(10)]):
                decile_data = decile_pnl[decile_pnl["decile"] == decile]
                if len(decile_data) > 0:
                    x_values = pd.to_datetime(decile_data["datetime"]) if "datetime" in decile_data.columns else range(len(decile_data))
                    if i in [0, 9]:
                        ax.plot(x_values, decile_data["cumulative_pnl"],
                               label=f"D{decile}", linewidth=2.5, alpha=0.9)
                    else:
                        ax.plot(x_values, decile_data["cumulative_pnl"],
                               label=f"D{decile}", linewidth=1, alpha=0.6)

        # 设置x轴为年份格式
        if isinstance(x_values, pd.DatetimeIndex):
            ax.xaxis.set_major_locator(mdates.YearLocator())
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%y'))
            # 添加次要刻度定位器（月份）
            ax.xaxis.set_minor_locator(mdates.MonthLocator())
            ax.tick_params(axis='x', rotation=30, labelsize=8)
        else:
            # 对于非日期数据，设置次要刻度
            from matplotlib.ticker import AutoMinorLocator
            ax.xaxis.set_minor_locator(AutoMinorLocator())
            ax.yaxis.set_minor_locator(AutoMinorLocator())

        ax.set_ylabel("Cumulative PnL", fontsize=8)
        ax.set_title("Decile PnL Curves", fontsize=8, fontweight='bold')
        if not only_pos:
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

        # 添加网格线
        ax.grid(True, alpha=0.6, which='major', linestyle='-', linewidth=0.5)  # 主要网格线
        ax.grid(True, alpha=0.3, which='minor', linestyle=':', linewidth=0.3)  # 次要网格线，虚线样式

        # 显示第10组的夏普比率
        sharpe_text = f"D10 Sharpe: {annualized_sharpe:.3f}"
        ax.text(0.02, 0.98, sharpe_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    
    def _plot_time_info(self, ax, results):
        """绘制时间信息"""
        ax.axis('off')

        # 时间信息横向排列
        time_text = (f"Test Start: {results['test_start_time']}    "
                    f"Factor Computation: {results['factor_computation_time']:.2f}s    "
                    f"Total Backtest: {results['total_backtest_time']:.2f}s")

        ax.text(0.5, 0.5, time_text, ha='center', va='center',
               transform=ax.transAxes, fontsize=10, fontweight='bold')

    def _plot_basic_stats(self, ax, results):
        """绘制基本统计信息"""
        ax.axis('off')

        stats = results["factor_stats"]

        # 数据统计信息，横向排列
        stats_data = [
            ["Total Rows", f"{stats['total_rows']:,}"],
            ["Valid Rows", f"{stats['valid_rows']:,}"],
            ["Mean", f"{stats['mean']:.4f}"],
            ["Std", f"{stats['std']:.4f}"],
            ["Min", f"{stats['min']:.4f}"],
            ["Max", f"{stats['max']:.4f}"]
        ]

        table = ax.table(cellText=stats_data,
                        colLabels=['Metric', 'Value'],
                        cellLoc='center',
                        loc=(0, 0.1, 1, 0.7),  # 调整表格位置：(left, bottom, width, height)
                        colWidths=[0.4, 0.6])
        table.auto_set_font_size(False)
        table.set_fontsize(10)  # 增大字体
        table.scale(1, 1.2)     # 调整表格高度


    def _plot_global_ic(self, ax, results):
        """绘制全局IC指标"""
        ax.axis('off')

        if "error" in results["ic_results"]:
            ax.text(0.5, 0.5, f"IC Error: {results['ic_results']['error']}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            return

        global_ic = results["ic_results"]["global_ic"]

        # 全局IC数据
        ic_data = [
            ["Mean IC", f"{global_ic['mean_ic']:.4f}"],
            ["ICIR", f"{global_ic['icir']:.4f}"],
            ["IC>0 Ratio", f"{global_ic['ic_positive_ratio']:.1%}"],
            ["IC Count", f"{global_ic['ic_count']:,}"],
            ["Avg XS Size", f"{global_ic['xs_size_mean']:.1f}"]
        ]

        table = ax.table(cellText=ic_data,
                        colLabels=['Global IC', 'Value'],
                        cellLoc='center',
                        loc=(0, 0.1, 1, 0.7),  # 调整表格位置
                        colWidths=[0.5, 0.5])
        table.auto_set_font_size(False)
        table.set_fontsize(11)  # 增大字体
        table.scale(1, 1.3)     # 调整表格高度

        # 突出显示Mean IC - 设置第一行数据为蓝色
        for i in range(2):  # 2列
            cell = table[(1, i)]  # 第一行数据（索引1，因为0是标题）
            if i == 1:  # Value列
                cell.set_text_props(weight='bold', color='blue')
            cell.set_facecolor('#E6F3FF')  # 浅蓝色背景)

    def _plot_time_ic(self, ax, results):
        """绘制分时间点IC指标"""
        ax.axis('off')

        if "error" in results["ic_results"]:
            ax.text(0.5, 0.5, "IC Error", ha='center', va='center', transform=ax.transAxes, fontsize=10)
            return

        time_stats = results["ic_results"]["time_ic_stats"]

        # 分时间点IC数据
        ic_data = []
        for time_point in self.specific_times:
            if time_point in time_stats:
                stats = time_stats[time_point]
                ic_data.append([
                    time_point,
                    f"{stats['mean_ic']:.4f}",
                    f"{stats['icir']:.4f}"
                ])

        if ic_data:
            table = ax.table(cellText=ic_data,
                            colLabels=['Time', 'Mean IC', 'ICIR'],
                            cellLoc='center',
                            loc=(0, 0.1, 1, 0.7),  # 调整表格位置
                            colWidths=[0.3, 0.35, 0.35])
            table.auto_set_font_size(False)
            table.set_fontsize(10)  # 增大字体
            table.scale(1, 1.2)     # 调整表格高度

    def _plot_distribution(self, ax, results):
        """绘制因子分布图"""
        if "error" in results["distribution_data"]:
            ax.text(0.5, 0.5, f"Distribution Error: {results['distribution_data']['error']}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            return

        values = results["distribution_data"]["values"]
        if len(values) == 0:
            ax.text(0.5, 0.5, "No Valid Data", ha='center', va='center', transform=ax.transAxes)
            return

        ax.hist(values, bins=30, alpha=0.7, color='lightblue', edgecolor='navy', linewidth=0.5)
        ax.set_title('Factor Distribution', fontsize=8, fontweight='bold')
        ax.set_xlabel('Factor Value', fontsize=8)
        ax.set_ylabel('Frequency', fontsize=8)
        ax.tick_params(axis='both', which='major', labelsize=9)
        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)

    def _plot_ic_trend(self, ax, results):
        """绘制IC趋势图"""
        if "error" in results["ic_results"]:
            ax.text(0.5, 0.5, f"IC Error: {results['ic_results']['error']}",
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            return

        time_stats = results["ic_results"]["time_ic_stats"]

        # 提取时间点和对应的IC、ICIR值
        time_points = []
        mean_ics = []
        icirs = []

        for time_point in self.specific_times:
            if time_point in time_stats and time_stats[time_point]['ic_count'] > 0:
                time_points.append(time_point)
                mean_ics.append(time_stats[time_point]['mean_ic'])
                icirs.append(time_stats[time_point]['icir'])

        if not time_points:
            ax.text(0.5, 0.5, "No IC Trend Data", ha='center', va='center', transform=ax.transAxes)
            return

        # 绘制双轴图
        ax2 = ax.twinx()

        # 绘制Mean IC
        ax.plot(time_points, mean_ics, 'o-', color='blue', linewidth=2, markersize=6, label='Mean IC')
        ax.set_ylabel('Mean IC', color='blue', fontsize=8)
        ax.tick_params(axis='y', labelcolor='blue', labelsize=8)

        # 绘制ICIR
        ax2.plot(time_points, icirs, 's-', color='red', linewidth=2, markersize=6, label='ICIR')
        ax2.set_ylabel('ICIR', color='red', fontsize=8)
        ax2.tick_params(axis='y', labelcolor='red', labelsize=8)

        # 添加零线
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)

        # 设置x轴
        ax.set_xlabel('Time Points', fontsize=8)
        ax.tick_params(axis='x', labelsize=9)
        ax.set_title('IC Trend by Time Points', fontsize=8, fontweight='bold', pad=10)

        # 添加图例
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=9)

        ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.5)

    def _plot_monthly_ic_heatmap(self, ax, results):
        """绘制月度 IC 热力图"""
        ax.clear()  # 保证是干净的坐标轴

        # 1️⃣ 先检查数据有效性
        ic_res = results.get("ic_results", {})
        if ("error" in ic_res) or (ic_res.get("monthly_ic_data") is None):
            ax.axis("off")
            ax.text(0.5, 0.5,
                    "No monthly-IC data",
                    ha="center", va="center",
                    transform=ax.transAxes, fontsize=10)
            return

        # 2️⃣ 整理成 Year×Month 的透视表
        import pandas as pd   # 仅在本函数内部用到

        monthly_pd = ic_res["monthly_ic_data"].copy()
        # Series → DataFrame（若本身就是 DataFrame 也兼容）
        if isinstance(monthly_pd, pd.Series):
            monthly_pd = monthly_pd.to_frame("mean_ic")
        monthly_pd = monthly_pd.reset_index(names="year_month")

        # 拆年、月
        monthly_pd["year"]  = pd.to_datetime(monthly_pd["year_month"]).dt.year
        monthly_pd["month"] = pd.to_datetime(monthly_pd["year_month"]).dt.month

        heatmap_data = (
            monthly_pd
            .pivot(index="year", columns="month", values="mean_ic")
            .sort_index(ascending=False)                # 年份从上往下递减
            .reindex(columns=range(1, 13))              # 确保 1-12 月都在
        )

        # 3️⃣ 画热力图
        sns.heatmap(
            heatmap_data,
            ax=ax,
            cmap="RdBu_r",
            center=0,             # 0 作为色谱中心
            linewidths=.4,
            linecolor="white",
            cbar_kws={"shrink": .8, "label": "Mean IC"}
        )

        # 4️⃣ 美化坐标轴
        ax.set_xlabel("Month", fontsize=10)
        ax.set_ylabel("Year",  fontsize=10)
        ax.set_title("Monthly IC Heatmap", fontsize=8, fontweight="bold", pad=12)
        ax.set_xticklabels(ax.get_xticklabels(), rotation=0, fontsize=8)
        ax.set_yticklabels(ax.get_yticklabels(), rotation=0, fontsize=8)

    def _generate_json_report(self, results: Dict[str, Any], json_path: Path) -> None:
        """生成JSON报告"""
        # 清理不能序列化的数据
        clean_results = results.copy()

        # 移除分布数据中的大数组，只保留统计信息
        if "distribution_data" in clean_results and "values" in clean_results["distribution_data"]:
            values = clean_results["distribution_data"]["values"]
            clean_results["distribution_data"] = {
                "sample_size": clean_results["distribution_data"]["sample_size"],
                "value_count": len(values),
            }

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(clean_results, f, indent=2, ensure_ascii=False, default=str)

    def single_backtest_factors(self, factor_num: int, factor_dir: str = "alpha299_polars") -> None:
        """
        """
        factor_path = Path(factor_dir) / f"factor_{factor_num}.py"
        if not factor_path.exists():
            print(f"因子文件不存在: {factor_path}")
            return {}

        factor_expr = self._import_factor(factor_path)
        results = self.backtest_factor(factor_expr, f"factor_{factor_num}")
        # self.generate_report(results, output_dir)
        return results

    def batch_backtest_factors(self, factor_dir: str = "alpha299_polars",
                              output_dir: str = "testTmp/alpha299") -> Dict[str, Any]:
        """批量回测所有因子"""
        factor_path = Path(factor_dir)
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 获取所有因子文件
        factor_files = list(factor_path.glob("factor_*.py"))
        factor_files.sort(key=lambda x: int(x.stem.split('_')[1]))

        print(f"发现 {len(factor_files)} 个因子文件")

        batch_results = {
            "batch_start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_factors": len(factor_files),
            "successful_factors": 0,
            "failed_factors": 0,
            "factor_results": {},
            "summary_stats": {}
        }

        for i, factor_file in enumerate(factor_files, 1):
            factor_name = factor_file.stem
            # if factor_name not in ['factor_236', 'factor_267', 'factor_328', 'factor_200', 'factor_300_xs', 'factor_205', 'factor_187', 'factor_190', 'factor_220', 'factor_177', 'factor_147_xs']:
            #     continue
            if factor_name in ['factor_298', 'factor_147', 'factor_259', 'factor_284', 'factor_10', 'factor_30', 'factor_392', 'factor_144_xs', 'factor_259_xs', 'factor_147_xs']:
                continue
            print(f"\n[{i}/{len(factor_files)}] 正在处理: {factor_name}")

            # 检查是否已经回测过
            pdf_path = output_path / "img" / f"{factor_name}_report.pdf"
            if pdf_path.exists():
                print(f"  已经回测过，跳过")
                continue

            try:
                # 动态导入因子函数
                factor_expr = self._import_factor(factor_file)

                # 回测因子
                results = self.backtest_factor(factor_expr, factor_name)

                # 生成报告
                self.generate_report(results, output_dir)

                batch_results["factor_results"][factor_name] = results
                batch_results["successful_factors"] += 1

            except Exception as e:
                print(f"因子 {factor_name} 回测失败: {str(e)}")
                batch_results["factor_results"][factor_name] = {"error": str(e)}
                batch_results["failed_factors"] += 1

            finally:
                # 每个因子处理完后强制清理
                plt.close('all')
                gc.collect()
                
                # 每10个因子进行一次深度清理
                if i % 10 == 0:
                    print(f"  执行深度内存清理 ({i}/{len(factor_files)})")
                    import matplotlib
                    matplotlib.pyplot.close('all')
                    gc.collect()

        # 生成汇总统计
        batch_results["summary_stats"] = self._generate_summary_stats(batch_results)

        # 保存批量结果
        batch_json_path = output_path / "batch_results.json"
        with open(batch_json_path, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n批量回测完成!")
        print(f"成功: {batch_results['successful_factors']} 个")
        print(f"失败: {batch_results['failed_factors']} 个")
        print(f"汇总结果保存至: {batch_json_path}")

        return batch_results

    def _import_factor(self, factor_file: Path) -> pl.Expr:
        """动态导入因子函数"""
        import importlib.util
        import sys
        
        module_name = factor_file.stem
        module_name = module_name.split("_")[:2]
        module_name = "_".join(module_name)
        
        try:
            spec = importlib.util.spec_from_file_location(module_name, factor_file)
            module = importlib.util.module_from_spec(spec)
            
            # 临时添加到sys.modules
            sys.modules[module_name] = module
            spec.loader.exec_module(module)

            # 获取因子函数并调用
            factor_func = getattr(module, module_name)
            factor_expr = factor_func()
            
            return factor_expr
            
        except Exception as e:
            print(f"导入因子 {module_name} 失败: {e}")
            raise
        finally:
            # 清理模块引用
            if module_name in sys.modules:
                del sys.modules[module_name]
            if 'module' in locals():
                del module
            if 'spec' in locals():
                del spec
            gc.collect()

    def _generate_summary_stats(self, batch_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总统计"""
        successful_results = [
            result for result in batch_results["factor_results"].values()
            if "error" not in result and "ic_results" in result and "error" not in result["ic_results"]
        ]

        if not successful_results:
            return {"error": "无成功的因子结果"}

        # 提取IC统计
        mean_ics = [result["ic_results"]["global_ic"]["mean_ic"] for result in successful_results]
        icirs = [result["ic_results"]["global_ic"]["icir"] for result in successful_results]
        ic_positive_ratios = [result["ic_results"]["global_ic"]["ic_positive_ratio"] for result in successful_results]

        return {
            "successful_factor_count": len(successful_results),
            "mean_ic_stats": {
                "mean": float(np.mean(mean_ics)),
                "std": float(np.std(mean_ics)),
                "min": float(np.min(mean_ics)),
                "max": float(np.max(mean_ics))
            },
            "icir_stats": {
                "mean": float(np.mean(icirs)),
                "std": float(np.std(icirs)),
                "min": float(np.min(icirs)),
                "max": float(np.max(icirs))
            },
            "ic_positive_ratio_stats": {
                "mean": float(np.mean(ic_positive_ratios)),
                "std": float(np.std(ic_positive_ratios)),
                "min": float(np.min(ic_positive_ratios)),
                "max": float(np.max(ic_positive_ratios))
            }
        }


SAttn_GRU_demean = pl.read_parquet("model_factor/SAttn_GRU_demean.parquet")

df = df.hstack(SAttn_GRU_demean)

df

# from minute_factor_backtest import MinuteFactorBacktest

bt = MinuteFactorBacktest(mode="df")





df = df.rename({"SAttn_GRU_demean": "factor"})

res = bt.backtest_factor(df, "SAttn_GRU_demean")

bt._generate_pdf_report(res, only_pos=True)

df