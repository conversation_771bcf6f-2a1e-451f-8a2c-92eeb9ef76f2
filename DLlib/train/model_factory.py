"""
模型工厂函数

提供统一的模型创建接口，支持通过配置字典创建各种模型。
"""

from loguru import logger
from DLlib.models.master import GRU_SAttn, MASTER_TAttn_SAttn, TAttnOnly, SAttn_GRU
from DLlib.models.seq_models import GRUSeq


def create_model(model_name: str, model_params: dict):
    """
    模型工厂函数：根据模型名称和参数创建模型实例

    参数:
        model_name: 模型名称
        model_params: 模型参数字典，用户必须提供正确的参数名称和值

    返回:
        模型实例
    """
    if model_params is None:
        raise ValueError("model_params 不能为 None！必须提供完整的模型参数。")

    # 模型映射表
    model_registry = {
        "SAttn_GRU": SAttn_GRU,
        "GRU_SAttn": GRU_SAttn,
        "MASTER_TAttn_SAttn": MASTER_TAttn_SAttn,
        "TAttnOnly": TAttnOnly,
        "GRUSeq": GRUSeq,
    }

    if model_name not in model_registry:
        available_models = list(model_registry.keys())
        raise ValueError(f"未知的模型名称: {model_name}. 可用模型: {available_models}")

    model_class = model_registry[model_name]

    try:
        model = model_class(**model_params)
        logger.info(f"成功创建模型: {model_name}, 参数: {model_params}")
        return model
    except TypeError as e:
        logger.error(f"创建模型 {model_name} 时参数错误: {e}")
        logger.error(f"提供的参数: {model_params}")
        raise
