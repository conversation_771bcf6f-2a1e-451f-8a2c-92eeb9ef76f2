"""
模型工厂函数

提供统一的模型创建接口，支持通过配置字典创建各种模型。
自动发现和注册模型，无需手动维护映射表。
"""

import inspect
from loguru import logger
import DLlib.models.master as master_models
import DLlib.models.seq_models as seq_models
import torch.nn as nn


def _get_model_registry():
    """
    自动发现并注册所有可用的模型类

    返回:
        dict: 模型名称到模型类的映射
    """
    registry = {}

    # 从 master_models 模块中获取所有模型类
    for name, obj in inspect.getmembers(master_models):
        if (inspect.isclass(obj) and
            issubclass(obj, nn.Module) and
            obj != nn.Module and
            not name.startswith('_')):
            registry[name] = obj

    # 从 seq_models 模块中获取所有模型类
    for name, obj in inspect.getmembers(seq_models):
        if (inspect.isclass(obj) and
            issubclass(obj, nn.Module) and
            obj != nn.Module and
            not name.startswith('_')):
            registry[name] = obj

    return registry


def create_model(model_name: str, model_params: dict):
    """
    模型工厂函数：根据模型名称和参数创建模型实例

    参数:
        model_name: 模型名称
        model_params: 模型参数字典，用户必须提供正确的参数名称和值

    返回:
        模型实例
    """
    if model_params is None:
        raise ValueError("model_params 不能为 None！必须提供完整的模型参数。")

    # 自动获取模型映射表
    model_registry = _get_model_registry()

    if model_name not in model_registry:
        available_models = list(model_registry.keys())
        raise ValueError(f"未知的模型名称: {model_name}. 可用模型: {available_models}")

    model_class = model_registry[model_name]

    try:
        model = model_class(**model_params)
        logger.info(f"成功创建模型: {model_name}, 参数: {model_params}")
        return model
    except TypeError as e:
        logger.error(f"创建模型 {model_name} 时参数错误: {e}")
        logger.error(f"提供的参数: {model_params}")
        raise


def list_available_models():
    """
    列出所有可用的模型

    返回:
        list: 可用模型名称列表
    """
    return list(_get_model_registry().keys())
