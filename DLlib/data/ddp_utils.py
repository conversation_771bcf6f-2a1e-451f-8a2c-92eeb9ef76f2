"""
DDP 训练工具函数

提供分布式训练的设置和数据传输工具。
"""

import os
import random
import numpy as np
import torch
import torch.distributed as dist
from typing import Dict, Optional
from loguru import logger


def set_global_seed(seed: int = 666):
    """
    设置全局随机种子，确保实验的可重复性

    Args:
        seed: 随机种子值，默认为666
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

    # 设置确定性算法（可能会影响性能）
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

    # 设置环境变量确保完全确定性
    os.environ['PYTHONHASHSEED'] = str(seed)

    if is_main_process():
        logger.info(f"全局随机种子已设置为: {seed}")


def setup_dist():
    """
    设置分布式训练环境
    返回 local_rank 和 device
    """
    if not dist.is_available():
        raise RuntimeError("PyTorch 未编译分布式支持")
    
    # 初始化进程组
    import datetime as dt
    dist.init_process_group(backend="nccl", init_method="env://", timeout=dt.timedelta(minutes=150))
    
    # 获取本地 rank
    local_rank = int(os.environ["LOCAL_RANK"])     # torchrun 会注入
    world_size = dist.get_world_size()
    global_rank = dist.get_rank()
    
    # 设置当前进程的默认 CUDA 设备
    torch.cuda.set_device(local_rank)
    device = torch.device(f"cuda:{local_rank}")
    
    # 只在 rank 0 打印信息
    if global_rank == 0:
        logger.info(f"DDP 初始化完成: world_size={world_size}, local_rank={local_rank}")
    
    return local_rank, device


def cleanup_dist():
    """清理分布式训练环境"""
    if dist.is_initialized():
        dist.destroy_process_group()


def to_device_batch(batch: Dict[str, torch.Tensor], device: torch.device) -> Dict[str, torch.Tensor]:
    """
    将 batch 中的张量移动到指定设备
    处理 {"features","label","weight"} 格式
    """
    out = {}
    out["features"] = batch["features"].to(device, non_blocking=True)
    out["label"] = batch["label"].to(device, non_blocking=True)
    out["weight"] = batch["weight"].to(device, non_blocking=True)
    
    return out


def is_main_process() -> bool:
    """判断是否为主进程（rank 0）"""
    return not dist.is_initialized() or dist.get_rank() == 0


def get_world_size() -> int:
    """获取总进程数"""
    return dist.get_world_size() if dist.is_initialized() else 1


def get_rank() -> int:
    """获取当前进程的全局 rank"""
    return dist.get_rank() if dist.is_initialized() else 0
