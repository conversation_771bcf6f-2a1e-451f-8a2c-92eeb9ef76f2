"""
测试自动模型发现功能

验证模型工厂函数能够自动发现和注册所有可用的模型。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.train import create_model, list_available_models


def test_list_models():
    """测试列出所有可用模型"""
    print("=== 自动发现的模型列表 ===")
    models = list_available_models()
    for i, model_name in enumerate(sorted(models), 1):
        print(f"{i:2d}. {model_name}")
    print(f"\n总共发现 {len(models)} 个模型")
    return models


def test_create_sattn_gru():
    """测试创建 SAttn_GRU 模型"""
    print("\n=== 测试创建 SAttn_GRU 模型 ===")
    
    model_params = {
        "d_feat": 97,
        "d_atten_model": 48,
        "d_gru_model": 64,
        "s_nhead": 4,
        "dropout": 0.1,
        "gru_layers": 1
    }
    
    try:
        model = create_model("SAttn_GRU", model_params)
        print(f"✓ 成功创建 SAttn_GRU 模型: {type(model).__name__}")
        return True
    except Exception as e:
        print(f"✗ 创建 SAttn_GRU 模型失败: {e}")
        return False


def test_create_gruseq():
    """测试创建 GRUSeq 模型"""
    print("\n=== 测试创建 GRUSeq 模型 ===")
    
    model_params = {
        "f_in": 97,  # 注意：GRUSeq 使用 f_in 而不是 d_feat
        "hidden": 256,
        "layers": 1,
        "dropout": 0.1
    }
    
    try:
        model = create_model("GRUSeq", model_params)
        print(f"✓ 成功创建 GRUSeq 模型: {type(model).__name__}")
        return True
    except Exception as e:
        print(f"✗ 创建 GRUSeq 模型失败: {e}")
        return False


def test_invalid_model():
    """测试创建不存在的模型"""
    print("\n=== 测试创建不存在的模型 ===")
    
    try:
        model = create_model("NonExistentModel", {"param": 1})
        print("✗ 应该抛出异常但没有")
        return False
    except ValueError as e:
        print(f"✓ 正确抛出异常: {e}")
        return True
    except Exception as e:
        print(f"✗ 抛出了意外的异常: {e}")
        return False


def test_model_with_wrong_params():
    """测试使用错误参数创建模型"""
    print("\n=== 测试使用错误参数创建模型 ===")
    
    # 故意使用错误的参数
    wrong_params = {
        "wrong_param": 123,
        "another_wrong_param": "test"
    }
    
    try:
        model = create_model("SAttn_GRU", wrong_params)
        print("✗ 应该抛出异常但没有")
        return False
    except TypeError as e:
        print(f"✓ 正确抛出参数错误异常: {e}")
        return True
    except Exception as e:
        print(f"✗ 抛出了意外的异常: {e}")
        return False


def main():
    """主测试函数"""
    print("自动模型发现功能测试\n")
    
    # 测试列出模型
    available_models = test_list_models()
    
    # 测试创建各种模型
    tests = [
        test_create_sattn_gru,
        test_create_gruseq,
        test_invalid_model,
        test_model_with_wrong_params,
    ]
    
    results = []
    for test_func in tests:
        results.append(test_func())
    
    # 总结
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！自动模型发现功能正常工作。")
    else:
        print("❌ 部分测试失败，请检查代码。")
    
    print(f"\n现在你可以：")
    print(f"1. 在任何地方添加新的模型类到 DLlib.models 模块")
    print(f"2. 无需修改 model_factory.py")
    print(f"3. 新模型会自动被发现和注册")
    print(f"4. 使用 list_available_models() 查看所有可用模型")


if __name__ == "__main__":
    main()
