"""
手动模型配置示例

展示如何手动配置不同的模型参数，实现你想要的效果：
传入一个名字和一些参数，就可以创建对应的模型。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from projs.stock1m.scripts.pipelines.day_section import Config, create_model


def example_sattn_gru():
    """示例：配置 SAttn_GRU 模型"""
    print("=== SAttn_GRU 模型配置示例 ===")
    
    cfg = Config()
    cfg.model_name = "SAttn_GRU"
    cfg.model_params = {
        "d_atten_model": 48,
        "d_gru_model": 64,
        "s_nhead": 4,
        "dropout": 0.1,
        "gru_layers": 1
    }
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def example_gru_sattn():
    """示例：配置 GRU_SAttn 模型"""
    print("=== GRU_SAttn 模型配置示例 ===")
    
    cfg = Config()
    cfg.model_name = "GRU_SAttn"
    cfg.model_params = {
        "d_gru_model": 256,
        "d_atten_model": 128,
        "s_nhead": 2,
        "dropout": 0.1,
        "gru_layers": 1
    }
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def example_master():
    """示例：配置 MASTER_TAttn_SAttn 模型"""
    print("=== MASTER_TAttn_SAttn 模型配置示例 ===")
    
    cfg = Config()
    cfg.model_name = "MASTER_TAttn_SAttn"
    cfg.model_params = {
        "d_t_model": 128,
        "d_atten_model": 64,
        "t_nhead": 4,
        "s_nhead": 2,
        "dropout": 0.1,
        "use_sdpa": True
    }
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def example_tattn_only():
    """示例：配置 TAttnOnly 模型"""
    print("=== TAttnOnly 模型配置示例 ===")
    
    cfg = Config()
    cfg.model_name = "TAttnOnly"
    cfg.model_params = {
        "d_t_model": 184,
        "t_nhead": 2,
        "dropout": 0.1,
        "use_sdpa": True
    }
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def example_gruseq():
    """示例：配置 GRUSeq 模型"""
    print("=== GRUSeq 模型配置示例 ===")
    
    cfg = Config()
    cfg.model_name = "GRUSeq"
    cfg.model_params = {
        "hidden": 256,
        "layers": 1,
        "dropout": 0.1
    }
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def example_custom_config():
    """示例：自定义配置"""
    print("=== 自定义配置示例 ===")
    
    # 你可以随意调整参数
    cfg = Config()
    cfg.model_name = "SAttn_GRU"
    cfg.model_params = {
        "d_atten_model": 96,    # 增大注意力维度
        "d_gru_model": 128,     # 增大GRU维度
        "s_nhead": 8,           # 增加注意力头数
        "dropout": 0.2,         # 增大dropout
        "gru_layers": 2         # 增加GRU层数
    }
    
    # 同时可以修改实验名称
    cfg.exp_name = f"daysection{cfg.timestamp}_SAttn_GRU_large_demean"
    cfg.ckpt_root = f"/home/<USER>/tslib/projs/stock1m/checkpoints/{cfg.exp_name}"
    
    print(f"模型名称: {cfg.model_name}")
    print(f"模型参数: {cfg.model_params}")
    print(f"实验名称: {cfg.exp_name}")
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=97, model_params=cfg.model_params)
    print(f"✓ 成功创建模型: {type(model).__name__}")
    print()


def show_usage_pattern():
    """展示使用模式"""
    print("=== 使用模式总结 ===")
    print("""
    # 基本使用模式：
    cfg = Config()
    cfg.model_name = "模型名称"
    cfg.model_params = {
        "参数1": 值1,
        "参数2": 值2,
        # ... 其他参数
    }
    
    # 创建模型
    model = create_model(cfg.model_name, d_feat=特征维度, model_params=cfg.model_params)
    
    # 支持的模型：
    - SAttn_GRU: 截面注意力 + GRU
    - GRU_SAttn: GRU + 截面注意力  
    - MASTER_TAttn_SAttn: 时序注意力 + 截面注意力
    - TAttnOnly: 仅时序注意力
    - GRUSeq: 传统GRU序列模型
    """)


if __name__ == "__main__":
    print("手动模型配置示例\n")
    
    # 展示使用模式
    show_usage_pattern()
    
    # 运行各种示例
    example_sattn_gru()
    example_gru_sattn()
    example_master()
    example_tattn_only()
    example_gruseq()
    example_custom_config()
    
    print("所有示例运行完成！")
    print("\n现在你可以在 day_section.py 中直接修改 Config 类来配置你想要的模型了。")
