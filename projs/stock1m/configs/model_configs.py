"""
模型配置文件

提供各种预定义的模型配置，可以直接在 day_section.py 中使用。
"""

# SAttn_GRU 模型的不同配置
SATTN_GRU_CONFIGS = {
    "default": {
        "d_atten_model": 48,
        "d_gru_model": 64,
        "s_nhead": 4,
        "dropout": 0.1,
        "gru_layers": 1
    },
    "large": {
        "d_atten_model": 128,
        "d_gru_model": 256,
        "s_nhead": 8,
        "dropout": 0.1,
        "gru_layers": 2
    },
    "small": {
        "d_atten_model": 32,
        "d_gru_model": 48,
        "s_nhead": 2,
        "dropout": 0.1,
        "gru_layers": 1
    }
}

# GRU_SAttn 模型的不同配置
GRU_SATTN_CONFIGS = {
    "default": {
        "d_gru_model": 256,
        "d_atten_model": 128,
        "s_nhead": 2,
        "dropout": 0.1,
        "gru_layers": 1
    },
    "large": {
        "d_gru_model": 512,
        "d_atten_model": 256,
        "s_nhead": 4,
        "dropout": 0.1,
        "gru_layers": 2
    }
}

# MASTER_TAttn_SAttn 模型的不同配置
MASTER_CONFIGS = {
    "default": {
        "d_t_model": 128,
        "d_atten_model": 64,
        "t_nhead": 4,
        "s_nhead": 2,
        "dropout": 0.1,
        "use_sdpa": True
    },
    "large": {
        "d_t_model": 256,
        "d_atten_model": 128,
        "t_nhead": 8,
        "s_nhead": 4,
        "dropout": 0.1,
        "use_sdpa": True
    }
}

# TAttnOnly 模型的不同配置
TATTN_ONLY_CONFIGS = {
    "default": {
        "d_t_model": 184,
        "t_nhead": 2,
        "dropout": 0.1,
        "use_sdpa": True
    },
    "large": {
        "d_t_model": 256,
        "t_nhead": 4,
        "dropout": 0.1,
        "use_sdpa": True
    }
}

# GRUSeq 模型的不同配置
GRUSEQ_CONFIGS = {
    "default": {
        "hidden": 256,
        "layers": 1,
        "dropout": 0.1
    },
    "large": {
        "hidden": 512,
        "layers": 2,
        "dropout": 0.1
    },
    "small": {
        "hidden": 128,
        "layers": 1,
        "dropout": 0.1
    }
}

# 预定义的完整模型配置
PREDEFINED_MODEL_CONFIGS = {
    # SAttn_GRU 的不同变体
    "SAttn_GRU": SATTN_GRU_CONFIGS["default"],
    "SAttn_GRU_large": SATTN_GRU_CONFIGS["large"],
    "SAttn_GRU_small": SATTN_GRU_CONFIGS["small"],
    
    # GRU_SAttn 的不同变体
    "GRU_SAttn": GRU_SATTN_CONFIGS["default"],
    "GRU_SAttn_large": GRU_SATTN_CONFIGS["large"],
    
    # MASTER 的不同变体
    "MASTER_TAttn_SAttn": MASTER_CONFIGS["default"],
    "MASTER_TAttn_SAttn_large": MASTER_CONFIGS["large"],
    
    # TAttnOnly 的不同变体
    "TAttnOnly": TATTN_ONLY_CONFIGS["default"],
    "TAttnOnly_large": TATTN_ONLY_CONFIGS["large"],
    
    # GRUSeq 的不同变体
    "GRUSeq": GRUSEQ_CONFIGS["default"],
    "GRUSeq_large": GRUSEQ_CONFIGS["large"],
    "GRUSeq_small": GRUSEQ_CONFIGS["small"],
}


def get_model_config(model_name: str, variant: str = "default") -> dict:
    """
    获取指定模型的配置
    
    参数:
        model_name: 模型名称 (如 "SAttn_GRU", "GRU_SAttn" 等)
        variant: 变体名称 (如 "default", "large", "small")
    
    返回:
        模型配置字典
    """
    config_map = {
        "SAttn_GRU": SATTN_GRU_CONFIGS,
        "GRU_SAttn": GRU_SATTN_CONFIGS,
        "MASTER_TAttn_SAttn": MASTER_CONFIGS,
        "TAttnOnly": TATTN_ONLY_CONFIGS,
        "GRUSeq": GRUSEQ_CONFIGS,
    }
    
    if model_name not in config_map:
        available_models = list(config_map.keys())
        raise ValueError(f"未知的模型名称: {model_name}. 可用模型: {available_models}")
    
    model_configs = config_map[model_name]
    if variant not in model_configs:
        available_variants = list(model_configs.keys())
        raise ValueError(f"模型 {model_name} 没有变体 {variant}. 可用变体: {available_variants}")
    
    return model_configs[variant].copy()


def create_model_params_dict(model_name: str, variant: str = "default") -> dict:
    """
    创建适用于 Config.model_params 的字典格式
    
    参数:
        model_name: 模型名称
        variant: 变体名称
    
    返回:
        格式为 {model_name: config} 的字典
    """
    config = get_model_config(model_name, variant)
    return {model_name: config}


# 使用示例：
if __name__ == "__main__":
    # 示例1: 获取单个模型配置
    sattn_gru_config = get_model_config("SAttn_GRU", "large")
    print("SAttn_GRU large 配置:", sattn_gru_config)
    
    # 示例2: 创建适用于 Config 的模型参数字典
    model_params = create_model_params_dict("SAttn_GRU", "large")
    print("模型参数字典:", model_params)
    
    # 示例3: 查看所有预定义配置
    print("\n所有预定义配置:")
    for name, config in PREDEFINED_MODEL_CONFIGS.items():
        print(f"  {name}: {config}")
